<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biodata - Saputra Pramahkota Hati</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <div class="info-button-container">
                <button class="info-btn" id="infoBtn">
                    <i class="fas fa-info-circle"></i>
                    <span>Info</span>
                </button>
            </div>
            <div class="profile-image">
                <div class="image-placeholder">
                    <i class="fas fa-user"></i>
                </div>
            </div>
            <h1 class="name">Saputra Pramahkota Hati</h1>
            <p class="subtitle">Siswa Teknik Komputer dan Jaringan</p>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Personal Info Card -->
            <div class="card personal-info">
                <div class="card-header">
                    <i class="fas fa-user-circle"></i>
                    <h2>Informasi Pribadi</h2>
                </div>
                <div class="card-content">
                    <div class="info-item">
                        <span class="label">Nama Lengkap:</span>
                        <span class="value">Saputra Pramahkota Hati</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Tanggal Lahir:</span>
                        <span class="value">19 Juni 2008</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Kelas:</span>
                        <span class="value">10 TKJ A</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Alamat:</span>
                        <span class="value">Jl. Merdeka No. 123, Jakarta Selatan</span>
                    </div>
                </div>
            </div>

            <!-- Education Card -->
            <div class="card education">
                <div class="card-header">
                    <i class="fas fa-graduation-cap"></i>
                    <h2>Riwayat Pendidikan</h2>
                </div>
                <div class="card-content">
                    <div class="education-item">
                        <div class="education-period">2020 - Sekarang</div>
                        <div class="education-details">
                            <h3>SMK Negeri 1 Jakarta</h3>
                            <p>Teknik Komputer dan Jaringan (TKJ)</p>
                            <span class="status">Sedang Bersekolah</span>
                        </div>
                    </div>
                    <div class="education-item">
                        <div class="education-period">2017 - 2020</div>
                        <div class="education-details">
                            <h3>SMP Negeri 5 Jakarta</h3>
                            <p>Sekolah Menengah Pertama</p>
                            <span class="status completed">Lulus</span>
                        </div>
                    </div>
                    <div class="education-item">
                        <div class="education-period">2011 - 2017</div>
                        <div class="education-details">
                            <h3>SD Negeri 10 Jakarta</h3>
                            <p>Sekolah Dasar</p>
                            <span class="status completed">Lulus</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Skills Card -->
            <div class="card skills">
                <div class="card-header">
                    <i class="fas fa-code"></i>
                    <h2>Keahlian</h2>
                </div>
                <div class="card-content">
                    <div class="skill-item">
                        <span class="skill-name">Jaringan Komputer</span>
                        <div class="skill-bar">
                            <div class="skill-progress" data-width="85%"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <span class="skill-name">Hardware Komputer</span>
                        <div class="skill-bar">
                            <div class="skill-progress" data-width="80%"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <span class="skill-name">Troubleshooting</span>
                        <div class="skill-bar">
                            <div class="skill-progress" data-width="75%"></div>
                        </div>
                    </div>
                    <div class="skill-item">
                        <span class="skill-name">Microsoft Office</span>
                        <div class="skill-bar">
                            <div class="skill-progress" data-width="90%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Card -->
            <div class="card contact">
                <div class="card-header">
                    <i class="fas fa-envelope"></i>
                    <h2>Kontak</h2>
                </div>
                <div class="card-content">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+62 812-3456-7890</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fab fa-instagram"></i>
                        <span>@saputra_pramahkota</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Jakarta Selatan, Indonesia</span>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 Saputra Pramahkota Hati. All rights reserved.</p>
        </footer>
    </div>

    <!-- Floating Elements -->
    <div class="floating-elements">
        <div class="floating-circle circle-1"></div>
        <div class="floating-circle circle-2"></div>
        <div class="floating-circle circle-3"></div>
    </div>

    <!-- Info Popup Modal -->
    <div class="info-modal-overlay" id="infoModalOverlay">
        <div class="info-modal">
            <div class="info-modal-header">
                <h2><i class="fas fa-info-circle"></i> Informasi Website</h2>
                <button class="info-close-btn" id="infoCloseBtn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="info-modal-content">
                <div class="info-section">
                    <h3><i class="fas fa-globe"></i> Tentang Website</h3>
                    <p>Website biodata digital ini dibuat sebagai tugas praktek untuk menampilkan profil pribadi dengan desain modern dan interaktif.</p>
                </div>

                <div class="info-section">
                    <h3><i class="fas fa-user-graduate"></i> Pembuat</h3>
                    <div class="creator-info">
                        <div class="creator-detail">
                            <span class="label">Nama:</span>
                            <span class="value">Saputra Pramahkota Hati</span>
                        </div>
                        <div class="creator-detail">
                            <span class="label">Kelas:</span>
                            <span class="value">10 TKJ A</span>
                        </div>
                        <div class="creator-detail">
                            <span class="label">Sekolah:</span>
                            <span class="value">SMK Negeri 1 Jakarta</span>
                        </div>
                    </div>
                </div>

                <div class="info-section">
                    <h3><i class="fas fa-code"></i> Teknologi</h3>
                    <div class="tech-stack">
                        <span class="tech-item">HTML5</span>
                        <span class="tech-item">CSS3</span>
                        <span class="tech-item">JavaScript</span>
                        <span class="tech-item">Font Awesome</span>
                        <span class="tech-item">Google Fonts</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3><i class="fas fa-palette"></i> Fitur</h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Desain responsif dan modern</li>
                        <li><i class="fas fa-check"></i> Animasi CSS yang halus</li>
                        <li><i class="fas fa-check"></i> Efek glassmorphism</li>
                        <li><i class="fas fa-check"></i> Popup interaktif untuk detail</li>
                        <li><i class="fas fa-check"></i> Soft color palette</li>
                    </ul>
                </div>

                <div class="info-section">
                    <h3><i class="fas fa-calendar"></i> Informasi Pembuatan</h3>
                    <div class="creation-info">
                        <div class="creator-detail">
                            <span class="label">Dibuat pada:</span>
                            <span class="value">Desember 2024</span>
                        </div>
                        <div class="creator-detail">
                            <span class="label">Versi:</span>
                            <span class="value">1.0</span>
                        </div>
                        <div class="creator-detail">
                            <span class="label">Status:</span>
                            <span class="value status-active">Aktif</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Overlay for Card Expansion -->
    <div class="modal-overlay" id="modalOverlay"></div>

    <script src="script.js"></script>
</body>
</html>
