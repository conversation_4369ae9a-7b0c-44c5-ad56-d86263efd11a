/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg,
        rgba(102, 126, 234, 0.1) 0%,
        rgba(118, 75, 162, 0.1) 25%,
        rgba(255, 154, 158, 0.1) 50%,
        rgba(250, 208, 196, 0.1) 75%,
        rgba(212, 252, 121, 0.1) 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    z-index: -2;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 3rem;
    animation: fadeInDown 1s ease-out;
}

.profile-image {
    margin-bottom: 1.5rem;
    animation: float 3s ease-in-out infinite;
}

.image-placeholder {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.image-placeholder:hover {
    transform: scale(1.05) rotate(5deg);
}

.image-placeholder i {
    font-size: 4rem;
    color: #667eea;
    opacity: 0.7;
}

.name {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    animation: textGlow 2s ease-in-out infinite alternate;
}

.subtitle {
    font-size: 1.2rem;
    color: #6b7280;
    font-weight: 300;
}

/* Card Styles */
.main-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
    transition: all 0.3s ease;
    animation: fadeInUp 1s ease-out;
    animation-fill-mode: both;
}

.card:nth-child(1) { animation-delay: 0.1s; }
.card:nth-child(2) { animation-delay: 0.2s; }
.card:nth-child(3) { animation-delay: 0.3s; }
.card:nth-child(4) { animation-delay: 0.4s; }

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(31, 38, 135, 0.5);
}

.card-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.card-header i {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    color: #667eea;
    animation: pulse 2s infinite;
}

.card-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #374151;
}

/* Personal Info Styles */
.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding-left: 1rem;
    padding-right: 1rem;
}

.label {
    font-weight: 500;
    color: #4b5563;
}

.value {
    font-weight: 400;
    color: #1f2937;
}

/* Education Styles */
.education-item {
    display: flex;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    transition: all 0.3s ease;
}

.education-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateX(10px);
}

.education-period {
    min-width: 120px;
    font-weight: 600;
    color: #667eea;
    font-size: 0.9rem;
}

.education-details h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.education-details p {
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    background: #fef3c7;
    color: #d97706;
}

.status.completed {
    background: #d1fae5;
    color: #059669;
}

/* Skills Styles */
.skill-item {
    margin-bottom: 1.5rem;
}

.skill-name {
    display: block;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.skill-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    width: 0;
    animation: fillSkill 2s ease-out forwards;
    animation-delay: 1s;
}

/* Contact Styles */
.contact-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding-left: 1rem;
    padding-right: 1rem;
}

.contact-item i {
    width: 20px;
    margin-right: 1rem;
    color: #667eea;
}

/* Footer */
.footer {
    text-align: center;
    padding: 2rem 0;
    color: #6b7280;
    animation: fadeIn 1s ease-out 1.5s both;
}

/* Floating Elements */
.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    animation: floatAround 20s infinite linear;
}

.circle-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: -7s;
}

.circle-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-delay: -14s;
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes textGlow {
    from { text-shadow: 0 0 20px rgba(102, 126, 234, 0.5); }
    to { text-shadow: 0 0 30px rgba(118, 75, 162, 0.8); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes fillSkill {
    from { width: 0; }
    to { width: var(--width); }
}

/* Skill Progress Specific Widths */
.skill-progress[data-width="85%"] { --width: 85%; }
.skill-progress[data-width="80%"] { --width: 80%; }
.skill-progress[data-width="75%"] { --width: 75%; }
.skill-progress[data-width="90%"] { --width: 90%; }

@keyframes floatAround {
    0% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
    100% { transform: translateY(0px) rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .name {
        font-size: 2rem;
    }

    .education-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .education-period {
        margin-bottom: 0.5rem;
    }
}
